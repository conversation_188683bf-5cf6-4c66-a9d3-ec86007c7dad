import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Voyage } from '../types';
import { getVoyageColor } from '../utils/colors';
import '../styles/voyage-selector.css';

interface VoyageDropdownProps {
  voyages: Voyage[];
  selectedImei: string;
  selectedVoyage: string | null;
  onVoyageSelect: (voyageId: string | null) => void;
}

export const VoyageDropdown: React.FC<VoyageDropdownProps> = ({
  voyages,
  selectedImei,
  selectedVoyage,
  onVoyageSelect
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Filter voyages for selected IMEI
  const filteredVoyages = voyages.filter(v => v.imei === selectedImei);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isOpen]);

  // Update position on window resize
  useEffect(() => {
    const handleResize = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  const updateDropdownPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 4,
        left: rect.left,
        width: rect.width
      });
    }
  };

  const handleToggleDropdown = () => {
    if (!isOpen) {
      updateDropdownPosition();
    }
    setIsOpen(!isOpen);
  };

  const handleVoyageClick = (voyageId: string | null) => {
    onVoyageSelect(voyageId);
    setIsOpen(false);
  };

  const getSelectedVoyageLabel = () => {
    if (!selectedVoyage) {
      return 'Tutti i viaggi';
    }
    
    const voyage = filteredVoyages.find(v => v.id === selectedVoyage);
    if (voyage) {
      const date = new Date(voyage.startTime);
      return `${date.toLocaleDateString()} - ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    return 'Viaggio selezionato';
  };

  const getSelectedVoyageColor = () => {
    if (!selectedVoyage) {
      return null;
    }

    const voyage = filteredVoyages.find(v => v.id === selectedVoyage);
    return voyage ? getVoyageColor(voyage.id) : null;
  };

  return (
    <div className="voyage-selector-container" ref={dropdownRef}>
      <button
        ref={buttonRef}
        className={`voyage-selector-button ${isOpen ? 'open' : ''}`}
        onClick={handleToggleDropdown}
        type="button"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label="Select voyage"
      >
        <div className="voyage-selector-content">
          {getSelectedVoyageColor() && (
            <div 
              className="voyage-color-indicator"
              style={{ backgroundColor: getSelectedVoyageColor()! }}
            />
          )}
          <span className="voyage-selector-text">
            {getSelectedVoyageLabel()}
          </span>
        </div>
        <svg 
          className={`voyage-selector-arrow ${isOpen ? 'rotated' : ''}`}
          width="12" 
          height="12" 
          viewBox="0 0 12 12"
          aria-hidden="true"
        >
          <path 
            d="M3 4.5L6 7.5L9 4.5" 
            stroke="currentColor" 
            strokeWidth="1.5" 
            fill="none"
          />
        </svg>
      </button>

      {isOpen && createPortal(
        <div
          className="voyage-selector-dropdown"
          role="listbox"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${Math.max(dropdownPosition.width, 200)}px`
          }}
        >
          <div
            className={`voyage-option ${!selectedVoyage ? 'selected' : ''}`}
            onClick={() => handleVoyageClick(null)}
            role="option"
            aria-selected={!selectedVoyage}
          >
            <span className="voyage-option-text">Tutti i viaggi</span>
          </div>


          {filteredVoyages.map(voyage => {
            const date = new Date(voyage.startTime);
            const label = `${date.toLocaleDateString()} - ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;

            return (
              <div
                key={voyage.id}
                className={`voyage-option ${selectedVoyage === voyage.id ? 'selected' : ''}`}
                onClick={() => handleVoyageClick(voyage.id)}
                role="option"
                aria-selected={selectedVoyage === voyage.id}
              >
                <div
                  className="voyage-color-indicator"
                  style={{ backgroundColor: getVoyageColor(voyage.id) }}
                />
                <span className="voyage-option-text">
                  {label}
                </span>
              </div>
            );
          })}
        </div>,
        document.body
      )}
    </div>
  );
};
