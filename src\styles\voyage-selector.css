/* Custom Voyage Selector with Color Indicators */
.voyage-selector-container {
  position: relative;
  display: inline-block;
  min-width: 180px;
  z-index: 10;
}

.voyage-selector-button {
  width: 100%;
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.voyage-selector-button:hover {
  border-color: #9ca3af;
  background-color: #f9fafb;
}

.voyage-selector-button:focus {
  outline: none;
  border-color: #4ade80;
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.voyage-selector-button.open {
  border-color: #4ade80;
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.voyage-selector-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.voyage-color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.voyage-selector-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
}

.voyage-selector-arrow {
  flex-shrink: 0;
  transition: transform 0.2s ease;
  color: #6b7280;
}

.voyage-selector-arrow.rotated {
  transform: rotate(180deg);
}

.voyage-selector-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 300px;
  overflow-y: auto;
  min-width: 200px;
}

.voyage-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-bottom: 1px solid #f3f4f6;
}

.voyage-option:last-child {
  border-bottom: none;
}

.voyage-option:hover {
  background-color: #f9fafb;
}

.voyage-option.selected {
  background-color: #ecfdf5;
  color: #065f46;
}

.voyage-option-text {
  flex: 1;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .voyage-selector-container {
    min-width: 150px;
  }
  
  .voyage-selector-text,
  .voyage-option-text {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .voyage-selector-container {
    min-width: auto;
    width: 100%;
  }
  
  .voyage-selector-dropdown {
    max-height: 200px;
  }
}

/* Animation for dropdown */
.voyage-selector-dropdown {
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling for dropdown */
.voyage-selector-dropdown::-webkit-scrollbar {
  width: 6px;
}

.voyage-selector-dropdown::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.voyage-selector-dropdown::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.voyage-selector-dropdown::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
