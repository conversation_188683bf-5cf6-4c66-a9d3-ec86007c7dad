/**
 * Color utility functions for trip visualization
 * Provides consistent color assignment for GPS tracking voyages
 */

// Predefined color palette for trip visualization
// Using distinct, accessible colors that work well on maps
const TRIP_COLORS = [
  '#ef4444', // red
  '#3b82f6', // blue
  '#10b981', // emerald
  '#f59e0b', // amber
  '#8b5cf6', // violet
  '#06b6d4', // cyan
  '#f97316', // orange
  '#84cc16', // lime
  '#ec4899', // pink
  '#6366f1', // indigo
  '#14b8a6', // teal
  '#eab308', // yellow
  '#dc2626', // red-600
  '#2563eb', // blue-600
  '#059669', // emerald-600
  '#d97706', // amber-600
  '#7c3aed', // violet-600
  '#0891b2', // cyan-600
  '#ea580c', // orange-600
  '#65a30d', // lime-600
];

/**
 * Generate a consistent color for a voyage based on its ID
 * @param voyageId - The unique identifier for the voyage
 * @returns A hex color string
 */
export const getVoyageColor = (voyageId: string): string => {
  // Create a simple hash from the voyage ID to ensure consistency
  let hash = 0;
  for (let i = 0; i < voyageId.length; i++) {
    const char = voyageId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Use absolute value and modulo to get a color index
  const colorIndex = Math.abs(hash) % TRIP_COLORS.length;
  return TRIP_COLORS[colorIndex];
};

/**
 * Get all available trip colors
 * @returns Array of hex color strings
 */
export const getTripColors = (): string[] => {
  return [...TRIP_COLORS];
};

/**
 * Generate a lighter version of a color for backgrounds or secondary elements
 * @param color - Hex color string (e.g., '#ef4444')
 * @param opacity - Opacity value between 0 and 1
 * @returns RGBA color string
 */
export const getLighterColor = (color: string, opacity: number = 0.3): string => {
  // Remove the # if present
  const hex = color.replace('#', '');
  
  // Parse RGB values
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * Check if a color is light or dark for contrast purposes
 * @param color - Hex color string
 * @returns true if the color is light, false if dark
 */
export const isLightColor = (color: string): boolean => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5;
};
