import React from 'react';
import { Voyage } from '../types';
import { getVoyageColor } from '../utils/colors';

interface VoyageSelectorProps {
  voyages: Voyage[];
  selectedVoyage: string | null;
  onVoyageSelect: (voyageId: string | null) => void;
}

export const VoyageSelector: React.FC<VoyageSelectorProps> = ({
  voyages,
  selectedVoyage,
  onVoyageSelect
}) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString();
  };

  const getVoyageDuration = (voyage: Voyage) => {
    if (!voyage.endTime) return 'Ongoing';
    
    const start = new Date(voyage.startTime).getTime();
    const end = new Date(voyage.endTime).getTime();
    const duration = end - start;
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Voyage Selection</h3>
        <p className="text-sm text-secondary">
          Select a voyage to view its track or show all messages
        </p>
      </div>
      
      <div className="card-body">
        <div className="mb-4">
          <button
            onClick={() => onVoyageSelect(null)}
            className={`btn w-full ${
              selectedVoyage === null ? 'btn-primary' : 'btn-secondary'
            }`}
          >
            Show All Messages
          </button>
        </div>

        <div className="space-y-2 max-h-96 overflow-y-auto">
          {voyages.length === 0 ? (
            <div className="text-center text-secondary py-4">
              No voyages available
            </div>
          ) : (
            voyages.map((voyage) => (
              <div
                key={voyage.id}
                onClick={() => onVoyageSelect(voyage.id)}
                className={`p-3 border rounded cursor-pointer transition-colors ${
                  selectedVoyage === voyage.id
                    ? 'border-primary-green bg-primary-green-light'
                    : 'border-border hover:border-border-hover hover:bg-background-secondary'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                      style={{ backgroundColor: getVoyageColor(voyage.id) }}
                      title={`Trip color: ${getVoyageColor(voyage.id)}`}
                    />
                    <div className="font-semibold text-sm">
                      IMEI: {voyage.imei}
                    </div>
                  </div>
                  <div className="text-xs text-secondary">
                    {voyage.messages.length} points
                  </div>
                </div>
                
                <div className="text-xs text-secondary space-y-1">
                  <div>
                    <strong>Start:</strong> {formatDate(voyage.startTime)} at {formatTime(voyage.startTime)}
                  </div>
                  {voyage.endTime && (
                    <div>
                      <strong>End:</strong> {formatDate(voyage.endTime)} at {formatTime(voyage.endTime)}
                    </div>
                  )}
                  <div>
                    <strong>Duration:</strong> {getVoyageDuration(voyage)}
                  </div>
                </div>
                
                {!voyage.endTime && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success text-white">
                      Active
                    </span>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
