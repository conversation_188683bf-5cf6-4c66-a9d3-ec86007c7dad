import WebSocket from 'ws';

console.log('🚀 Test Multiple IMEI - Creazione dati per test filtri');

const ws = new WebSocket('ws://localhost:8090/ws');

// Dati GPS per Roma (IMEI 1234567890)
const romaPositions = [
  { lat: 41.9028, lng: 12.4964, status: 'Inizio' },
  { lat: 41.9109, lng: 12.4818, status: 'GPS Fixed' },
  { lat: 41.8986, lng: 12.4768, status: 'GPS Fixed' },
  { lat: 41.9028, lng: 12.4964, status: 'Fine' }
];

// Dati GPS per Milano (IMEI 9876543210)
const milanoPositions = [
  { lat: 45.4642, lng: 9.1900, status: 'Inizio' },
  { lat: 45.4654, lng: 9.1859, status: 'GPS Fixed' },
  { lat: 45.4668, lng: 9.1905, status: 'GPS Fixed' },
  { lat: 45.4642, lng: 9.1900, status: 'Fine' }
];

// IMEI che non avrà dati (per testare azzeramento)
const emptyImei = '1111111111';

let messageIndex = 0;

ws.on('open', () => {
  console.log('✅ Connesso al server GPS');
  
  // Invia dati per Roma (IMEI 1234567890)
  console.log('🏛️ Invio dati GPS per Roma (IMEI 1234567890)...');
  sendPositions('1234567890', romaPositions, () => {
    
    // Invia dati per Milano (IMEI 9876543210)
    console.log('🏢 Invio dati GPS per Milano (IMEI 9876543210)...');
    sendPositions('9876543210', milanoPositions, () => {
      
      console.log('✅ Tutti i dati inviati!');
      console.log('📊 Dati creati:');
      console.log('   - IMEI 1234567890: 1 viaggio a Roma (4 messaggi)');
      console.log('   - IMEI 9876543210: 1 viaggio a Milano (4 messaggi)');
      console.log('   - IMEI 1111111111: Nessun dato (per test azzeramento)');
      
      setTimeout(() => {
        console.log('🔚 Chiusura connessione');
        ws.close();
      }, 1000);
    });
  });
});

function sendPositions(imei, positions, callback) {
  let index = 0;
  
  function sendNext() {
    if (index >= positions.length) {
      callback();
      return;
    }
    
    const pos = positions[index];
    const message = {
      type: 'gps_data',
      data: {
        imei: imei,
        timestamp: new Date().toISOString(),
        lat: pos.lat,
        lng: pos.lng,
        speed: pos.status === 'Inizio' || pos.status === 'Fine' ? 0 : Math.floor(Math.random() * 50) + 20,
        status: pos.status,
        battery: 95 - (messageIndex * 2)
      }
    };
    
    console.log(`📍 ${imei}: ${pos.status} - ${pos.lat}, ${pos.lng}`);
    ws.send(JSON.stringify(message));
    
    messageIndex++;
    index++;
    
    // Pausa tra i messaggi
    setTimeout(sendNext, 2000);
  }
  
  sendNext();
}

ws.on('close', () => {
  console.log('❌ Disconnesso dal server');
  process.exit(0);
});

ws.on('error', (error) => {
  console.error('🚨 Errore WebSocket:', error.message);
  process.exit(1);
});

// Gestione interruzione
process.on('SIGINT', () => {
  console.log('\n🛑 Interruzione manuale');
  ws.close();
  process.exit(0);
});
